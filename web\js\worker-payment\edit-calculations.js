/**
 * Модуль вычислений для формы редактирования платежа работнику
 * Содержит функции для математических операций и форматирования
 */

// Глобальный объект для функций вычислений редактирования
window.WorkerPaymentEditCalculations = {

    /**
     * Форматирование числа с разделителями тысяч
     */
    formatNumber: function (number) {
        if (!number || isNaN(number)) return '';
        return parseFloat(number).toLocaleString('ru-RU');
    },

    /**
     * Получение числового значения из строки с любой группировкой разрядов
     * Удаляем ВСЕ символы, кроме цифр, чтобы избежать ошибок при наличии
     * узких неразрывных пробелов (U+202F), обычных пробелов, запятых и др.
     */
    getNumericValue: function (formattedString) {
        if (!formattedString) return 0;

        // Удаляем все символы, которые НЕ являются цифрами
        var numericString = formattedString.toString().replace(/[^\d]/g, '');

        // Преобразуем в целое число
        var number = parseInt(numericString || '0', 10);

        return isNaN(number) ? 0 : number;
    },

    /**
     * Подсчет общей суммы всех выплат
     */
    calculateTotalAmount: function () {
        var total = 0;

        // Суммируем основные типы платежей
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var checkedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                // Суммируем из динамических полей
                dynamicAmounts.find('input[data-method]').each(function () {
                    var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    total += amount;
                });
            } else {
                // Берем из основного поля
                var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                total += amount;
            }
        });

        // Добавляем погашение долга
        if ($('#debt-payment-checkbox').prop('checked')) {
            var debtAmount = window.WorkerPaymentEditCalculations.getNumericValue($('#debt-payment-amount').val());
            total += debtAmount;
        }

        return total;
    },

    /**
     * Подсчет суммы зарплаты и аванса
     */
    calculateSalaryAndAdvanceTotal: function () {
        var total = 0;

        var salaryRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_SALARY + '"]');
        var advanceRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_ADVANCE + '"]');

        [salaryRow, advanceRow].forEach(function (row) {
            if (row.length > 0) {
                var typeCheckbox = row.find('.payment-type-checkbox');

                if (typeCheckbox.prop('checked')) {
                    var checkedMethods = row.find('.payment-method-checkbox:checked');
                    var amountInput = row.find('.amount-input');
                    var dynamicAmounts = row.find('.dynamic-amounts');

                    if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                        dynamicAmounts.find('input[data-method]').each(function () {
                            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                            total += amount;
                        });
                    } else {
                        var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                        total += amount;
                    }
                }
            }
        });

        return total;
    },

    /**
     * Подсчет оставшейся зарплаты
     */
    calculateRemainingSalary: function () {
        if (typeof WORKER_SALARY === 'undefined') return 0;

        var totalSalaryAndAdvance = this.calculateSalaryAndAdvanceTotal();
        return Math.max(0, WORKER_SALARY - totalSalaryAndAdvance);
    },

    /**
     * Валидация суммы относительно лимитов
     */
    validateAmountLimits: function (amount, type) {
        var limits = {
            isValid: true,
            message: ''
        };

        // Проверка для зарплаты и аванса
        if (type === WORKER_FINANCES_TYPE_SALARY || type === WORKER_FINANCES_TYPE_ADVANCE) {
            var remainingSalary = this.calculateRemainingSalary();

            if (amount > remainingSalary) {
                limits.isValid = false;
                limits.message = 'Сумма превышает оставшуюся зарплату: ' + this.formatNumber(remainingSalary);
            }
        }

        // Проверка для погашения долга
        if (type === WORKER_FINANCES_TYPE_DEBT_PAYMENT) {
            var maxDebt = WORKER_DEBT || 0;

            if (amount > maxDebt) {
                limits.isValid = false;
                limits.message = 'Сумма превышает размер долга: ' + this.formatNumber(maxDebt);
            }
        }

        return limits;
    },

    /**
     * Форматирование валюты
     */
    formatCurrency: function (amount, currency = 'сум') {
        return this.formatNumber(amount) + ' ' + currency;
    },

    /**
     * Округление до копеек
     */
    roundToCents: function (amount) {
        return Math.round(amount * 100) / 100;
    },

    /************************************
     * ДОПОЛНИТЕЛЬНЫЕ ФУНКЦИИ (как в форме создания)
     ************************************/

    /**
     * Форматирование ввода чисел с разделителями тысяч
     */
    formatNumberInput: function (value) {
        if (value === null || value === undefined || value === '') return '';

        try {
            var numericValue = value.toString().replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue, 10).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    },

    /**
     * Обработка ввода в основное поле суммы типа платежа
     */
    handleMainAmountInput: function (amountInput) {
        var paymentRow = amountInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');
        var enteredAmount = this.getNumericValue(amountInput.val());

        // Не применяем автоматическое распределение при загрузке данных или инициализации
        if (paymentRow.data('loading-data') || paymentRow.data('initializing')) {
            return;
        }

        // Если поле очищено, не применяем никакой логики
        var inputValue = amountInput.val();
        if (!inputValue || inputValue.trim() === '') {
            return;
        }

        // Проверяем лимиты для зарплаты и аванса
        if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

            // Только если введенная сумма превышает доступную, корректируем её
            if (enteredAmount > maxAllowedAmount) {
                enteredAmount = maxAllowedAmount;
                this.setValueSilently(amountInput, maxAllowedAmount > 0 ? this.formatNumberInput(maxAllowedAmount.toString()) : '');
            }
        }

        // Автоматическое распределение только для зарплаты и аванса и только если поля пустые
        if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var selectedMethods = paymentRow.find('.payment-method-checkbox:checked');
            var dynamicAmounts = paymentRow.find('.dynamic-amounts input');

            if (selectedMethods.length > 1 && dynamicAmounts.length > 1 && enteredAmount > 0) {
                // Проверяем, есть ли уже значения в динамических полях
                var hasExistingValues = false;
                dynamicAmounts.each(function () {
                    if ($(this).val() && $(this).val().trim() !== '') {
                        hasExistingValues = true;
                        return false; // break
                    }
                });

                // Распределяем только если поля пустые
                if (!hasExistingValues) {
                    this.distributeAmountToMethods(paymentRow, enteredAmount);
                }
            } else if (selectedMethods.length === 1 && dynamicAmounts.length === 1 && enteredAmount > 0) {
                // Если выбран только один метод и поле пустое, устанавливаем всю сумму на него
                if (!dynamicAmounts.first().val() || dynamicAmounts.first().val().trim() === '') {
                    this.setValueSilently(dynamicAmounts.first(), this.formatNumberInput(enteredAmount.toString()));
                }
            }
        }
    },

    /**
     * Упрощённое распределение остатка (динамические поля, когда пользователь изменяет одно из них)
     */
    simpleAutoDistribute: function (changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Не применяем автоматическое распределение при загрузке данных или инициализации
        if (paymentRow.data('loading-data') || paymentRow.data('initializing')) {
            return;
        }

        // Для типов платежей, отличных от зарплаты и аванса, не делаем автоматическое распределение
        // Пользователь должен сам вводить суммы в каждое поле
        if (paymentTypeId != WORKER_FINANCES_TYPE_SALARY && paymentTypeId != WORKER_FINANCES_TYPE_ADVANCE) {
            // Просто пересчитываем общую сумму на основе введенных значений
            this.updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Если поле очищено (пустое), просто пересчитываем сумму без принудительного заполнения
        var changedValue = changedInput.val();
        if (!changedValue || changedValue.trim() === '') {
            this.updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Получаем доступную сумму для зарплаты и аванса
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var availableAmount = Math.max(0, originalSalary - originalTotalPaid);

        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        if (dynamicInputs.length < 2) return; // Должно быть минимум 2 поля

        // Получаем текущие значения всех полей
        var currentAmounts = [];
        var totalCurrentAmount = 0;
        var changedInputIndex = -1;

        dynamicInputs.each(function (index) {
            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
            currentAmounts.push(amount);
            totalCurrentAmount += amount;

            if ($(this).is(changedInput)) {
                changedInputIndex = index;
            }
        });

        // Проверяем общую сумму всех полей
        var totalAmount = 0;
        dynamicInputs.each(function () {
            totalAmount += window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
        });

        // Если общая сумма превышает доступную, корректируем только измененное поле
        if (totalAmount > availableAmount) {
            var changedAmount = currentAmounts[changedInputIndex];
            var otherFieldsTotal = totalAmount - changedAmount;
            var maxForChangedField = Math.max(0, availableAmount - otherFieldsTotal);

            // Устанавливаем максимально возможное значение для измененного поля только если оно действительно превышает лимит
            if (changedAmount > maxForChangedField) {
                this.setValueSilently(changedInput, maxForChangedField > 0 ? this.formatNumberInput(maxForChangedField.toString()) : '');
            }
        }

        // Обновляем основное поле с фактической суммой всех полей
        this.updateMainAmountFromDynamicInputs(paymentRow);


    },

    /**
     * Автоматически распределяет оставшуюся сумму (для зарплаты и аванса) между методами оплаты
     */
    autoDistributeToMethods: function (row) {
        var typeId = row.data('type');
        var remainingAmount = 0;

        // Не применяем автоматическое распределение при загрузке данных или инициализации
        if (row.data('loading-data') || row.data('initializing')) {
            return;
        }

        // Получаем оставшуюся сумму для зарплаты и аванса
        if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            remainingAmount = Math.max(0, originalSalary - originalTotalPaid);
        }

        // Автоматическое распределение только если поля пустые
        if (remainingAmount > 0) {
            var dynamicInputs = row.find('.dynamic-amounts input');
            if (dynamicInputs.length === 2) {
                // Проверяем, есть ли уже значения в полях
                var hasExistingValues = false;
                dynamicInputs.each(function () {
                    if ($(this).val() && $(this).val().trim() !== '') {
                        hasExistingValues = true;
                        return false; // break
                    }
                });

                // Распределяем только если поля пустые
                if (!hasExistingValues) {
                    var cashInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_CASH + '"]');
                    var cardInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_PAYMENT_CARD + '"]');

                    if (cashInput.length && cardInput.length) {
                        // Оставляем поле наличных пустым, весь остаток на карту
                        this.setValueSilently(cashInput, '');
                        this.setValueSilently(cardInput, this.formatNumberInput(remainingAmount.toString()));

                        // Обновляем основное поле с общей суммой
                        var mainAmountInput = row.find('.amount-input');
                        this.setValueSilently(mainAmountInput, this.formatNumberInput(remainingAmount.toString()));
                    }
                }
            }
        }
    },

    /**
     * Обновляет основное поле суммы на основе значений в динамических полях
     */
    updateMainAmountFromDynamicInputs: function (paymentRow) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        var totalAmount = 0;

        dynamicInputs.each(function () {
            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
            totalAmount += amount;
        });

        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(totalAmount > 0 ? this.formatNumberInput(totalAmount.toString()) : '');
    },

    /**
     * Распределить введённую сумму между методами оплаты (двумя полями и т.д.)
     */
    distributeAmountToMethods: function (paymentRow, totalAmount) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');

        if (dynamicInputs.length === 2) {
            this.setValueSilently(dynamicInputs.first(), this.formatNumberInput(totalAmount.toString()));
            this.setValueSilently(dynamicInputs.last(), '');
        } else if (dynamicInputs.length > 2) {
            this.setValueSilently(dynamicInputs.first(), this.formatNumberInput(totalAmount.toString()));
            dynamicInputs.slice(1).each(function () {
                window.WorkerPaymentEditCalculations.setValueSilently($(this), '');
            });
        }
    },

    /**
     * Устанавливает значение в поле без вызова событий
     */
    setValueSilently: function (input, value) {
        input.data('processing', true);
        input.val(value);
        setTimeout(function () {
            input.removeData('processing');
        }, 10);
    }
};
