/**
 * Модуль вычислений для формы редактирования платежа работнику
 * Содержит функции для математических операций и форматирования
 */

// Глобальный объект для функций вычислений редактирования
window.WorkerPaymentEditCalculations = {

    /**
     * Форматирование числа с разделителями тысяч
     */
    formatNumber: function (number) {
        if (!number || isNaN(number)) return '';
        return parseFloat(number).toLocaleString('ru-RU');
    },

    /**
     * Получение числового значения из строки с любой группировкой разрядов
     * Удаляем ВСЕ символы, кроме цифр, чтобы избежать ошибок при наличии
     * узких неразрывных пробелов (U+202F), обычных пробелов, запятых и др.
     */
    getNumericValue: function (formattedString) {
        if (!formattedString) return 0;

        // Удаляем все символы, которые НЕ являются цифрами
        var numericString = formattedString.toString().replace(/[^\d]/g, '');

        // Преобразуем в целое число
        var number = parseInt(numericString || '0', 10);

        return isNaN(number) ? 0 : number;
    },

    /**
     * Подсчет общей суммы всех выплат
     */
    calculateTotalAmount: function () {
        var total = 0;

        // Суммируем основные типы платежей
        $('.payment-type-checkbox:checked').each(function () {
            var row = $(this).closest('tr');
            var checkedMethods = row.find('.payment-method-checkbox:checked');
            var amountInput = row.find('.amount-input');
            var dynamicAmounts = row.find('.dynamic-amounts');

            if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                // Суммируем из динамических полей
                dynamicAmounts.find('input[data-method]').each(function () {
                    var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                    total += amount;
                });
            } else {
                // Берем из основного поля
                var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                total += amount;
            }
        });

        // Добавляем погашение долга
        if ($('#debt-payment-checkbox').prop('checked')) {
            var debtAmount = window.WorkerPaymentEditCalculations.getNumericValue($('#debt-payment-amount').val());
            total += debtAmount;
        }

        return total;
    },

    /**
     * Подсчет суммы зарплаты и аванса
     */
    calculateSalaryAndAdvanceTotal: function () {
        var total = 0;

        var salaryRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_SALARY + '"]');
        var advanceRow = $('.payment-type-row[data-type="' + WORKER_FINANCES_TYPE_ADVANCE + '"]');

        [salaryRow, advanceRow].forEach(function (row) {
            if (row.length > 0) {
                var typeCheckbox = row.find('.payment-type-checkbox');

                if (typeCheckbox.prop('checked')) {
                    var checkedMethods = row.find('.payment-method-checkbox:checked');
                    var amountInput = row.find('.amount-input');
                    var dynamicAmounts = row.find('.dynamic-amounts');

                    if (checkedMethods.length > 1 && dynamicAmounts.is(':visible')) {
                        dynamicAmounts.find('input[data-method]').each(function () {
                            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
                            total += amount;
                        });
                    } else {
                        var amount = window.WorkerPaymentEditCalculations.getNumericValue(amountInput.val());
                        total += amount;
                    }
                }
            }
        });

        return total;
    },

    /**
     * Подсчет оставшейся зарплаты
     */
    calculateRemainingSalary: function () {
        if (typeof WORKER_SALARY === 'undefined') return 0;

        var totalSalaryAndAdvance = this.calculateSalaryAndAdvanceTotal();
        return Math.max(0, WORKER_SALARY - totalSalaryAndAdvance);
    },

    /**
     * Валидация суммы относительно лимитов
     */
    validateAmountLimits: function (amount, type) {
        var limits = {
            isValid: true,
            message: ''
        };

        // Проверка для зарплаты и аванса
        if (type === WORKER_FINANCES_TYPE_SALARY || type === WORKER_FINANCES_TYPE_ADVANCE) {
            var remainingSalary = this.calculateRemainingSalary();

            if (amount > remainingSalary) {
                limits.isValid = false;
                limits.message = 'Сумма превышает оставшуюся зарплату: ' + this.formatNumber(remainingSalary);
            }
        }

        // Проверка для погашения долга
        if (type === WORKER_FINANCES_TYPE_DEBT_PAYMENT) {
            var maxDebt = WORKER_DEBT || 0;

            if (amount > maxDebt) {
                limits.isValid = false;
                limits.message = 'Сумма превышает размер долга: ' + this.formatNumber(maxDebt);
            }
        }

        return limits;
    },

    /**
     * Форматирование валюты
     */
    formatCurrency: function (amount, currency = 'сум') {
        return this.formatNumber(amount) + ' ' + currency;
    },

    /**
     * Округление до копеек
     */
    roundToCents: function (amount) {
        return Math.round(amount * 100) / 100;
    },

    /************************************
     * ДОПОЛНИТЕЛЬНЫЕ ФУНКЦИИ (как в форме создания)
     ************************************/

    /**
     * Форматирование ввода чисел с разделителями тысяч
     */
    formatNumberInput: function (value) {
        if (value === null || value === undefined || value === '') return '';

        try {
            var numericValue = value.toString().replace(/[^\d]/g, '');
            if (numericValue === '') return '';
            return parseInt(numericValue, 10).toLocaleString('ru-RU');
        } catch (e) {
            console.warn('Error formatting number input:', value, e);
            return '';
        }
    },

    /**
     * Обработка ввода в основное поле суммы типа платежа
     */
    handleMainAmountInput: function (amountInput) {
        var paymentRow = amountInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');
        var enteredAmount = this.getNumericValue(amountInput.val());

        // Проверяем лимиты для зарплаты и аванса
        if (paymentTypeId == WORKER_FINANCES_TYPE_SALARY || paymentTypeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            var maxAllowedAmount = Math.max(0, originalSalary - originalTotalPaid);

            // Если введенная сумма превышает доступную, устанавливаем максимальную
            if (enteredAmount > maxAllowedAmount) {
                enteredAmount = maxAllowedAmount;
                amountInput.val(maxAllowedAmount > 0 ? this.formatNumberInput(maxAllowedAmount.toString()) : '');
            }
        }

        // Если есть выбранные методы оплаты, автоматически распределяем сумму
        var selectedMethods = paymentRow.find('.payment-method-checkbox:checked');
        var dynamicAmounts = paymentRow.find('.dynamic-amounts input');

        if (selectedMethods.length > 1 && dynamicAmounts.length > 1 && enteredAmount > 0) {
            // Распределяем сумму между методами оплаты
            this.distributeAmountToMethods(paymentRow, enteredAmount);
        } else if (selectedMethods.length === 1 && dynamicAmounts.length === 1 && enteredAmount > 0) {
            // Если выбран только один метод, устанавливаем всю сумму на него
            dynamicAmounts.first().val(this.formatNumberInput(enteredAmount.toString()));
        }
    },

    /**
     * Упрощённое распределение остатка (динамические поля, когда пользователь изменяет одно из них)
     */
    simpleAutoDistribute: function (changedInput) {
        var paymentRow = changedInput.closest('tr');
        var paymentTypeId = paymentRow.data('type');

        // Для типов платежей, отличных от зарплаты и аванса, не делаем автоматическое распределение
        // Пользователь должен сам вводить суммы в каждое поле
        if (paymentTypeId != WORKER_FINANCES_TYPE_SALARY && paymentTypeId != WORKER_FINANCES_TYPE_ADVANCE) {
            // Просто пересчитываем общую сумму на основе введенных значений
            this.updateMainAmountFromDynamicInputs(paymentRow);
            return;
        }

        // Получаем доступную сумму для зарплаты и аванса
        var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
        var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
        var availableAmount = Math.max(0, originalSalary - originalTotalPaid);

        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        if (dynamicInputs.length < 2) return; // Должно быть минимум 2 поля

        // Получаем текущие значения всех полей
        var currentAmounts = [];
        var totalCurrentAmount = 0;
        var changedInputIndex = -1;

        dynamicInputs.each(function (index) {
            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
            currentAmounts.push(amount);
            totalCurrentAmount += amount;

            if ($(this).is(changedInput)) {
                changedInputIndex = index;
            }
        });

        // Проверяем, не превышает ли введенная сумма доступную
        var changedAmount = currentAmounts[changedInputIndex];
        if (changedAmount > availableAmount) {
            // Если превышает, устанавливаем максимально доступную сумму
            changedAmount = availableAmount;
            changedInput.val(availableAmount > 0 ? this.formatNumberInput(availableAmount.toString()) : '');

            // Очищаем все остальные поля
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });
            return;
        }

        // Если есть остаток, распределяем его на следующее доступное поле
        var remainingAmount = availableAmount - changedAmount;

        if (remainingAmount > 0) {
            // Находим следующее поле (по кругу)
            var nextIndex = (changedInputIndex + 1) % dynamicInputs.length;
            var nextInput = dynamicInputs.eq(nextIndex);

            // Очищаем все поля кроме измененного
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });

            // Устанавливаем остаток в следующее поле
            nextInput.val(this.formatNumberInput(remainingAmount.toString()));
        } else {
            // Если остатка нет, очищаем все остальные поля
            dynamicInputs.each(function (index) {
                if (index !== changedInputIndex) {
                    $(this).val('');
                }
            });
        }

        // Обновляем основное поле с общей суммой
        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(this.formatNumberInput(availableAmount.toString()));


    },

    /**
     * Автоматически распределяет оставшуюся сумму (для зарплаты и аванса) между методами оплаты
     */
    autoDistributeToMethods: function (row) {
        var typeId = row.data('type');
        var remainingAmount = 0;

        // Получаем оставшуюся сумму для зарплаты и аванса
        if (typeId == WORKER_FINANCES_TYPE_SALARY || typeId == WORKER_FINANCES_TYPE_ADVANCE) {
            var originalSalary = parseFloat($('#worker-salary').text().replace(/[^\d.-]/g, '')) || 0;
            var originalTotalPaid = parseFloat($('#worker-total-paid').text().replace(/[^\d.-]/g, '')) || 0;
            remainingAmount = Math.max(0, originalSalary - originalTotalPaid);
        }

        if (remainingAmount > 0) {
            var dynamicInputs = row.find('.dynamic-amounts input');
            if (dynamicInputs.length === 2) {
                // Если есть два поля (наличные и карта), распределяем остаток на карту
                var cashInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_CASH + '"]');
                var cardInput = dynamicInputs.filter('[data-method="' + PAYMENT_TYPE_PAYMENT_CARD + '"]');

                if (cashInput.length && cardInput.length) {
                    // Оставляем поле наличных пустым, весь остаток на карту
                    cashInput.val('');
                    cardInput.val(this.formatNumberInput(remainingAmount.toString()));
                }
            }

            // Обновляем основное поле с общей суммой
            var mainAmountInput = row.find('.amount-input');
            mainAmountInput.val(this.formatNumberInput(remainingAmount.toString()));
        }
    },

    /**
     * Обновляет основное поле суммы на основе значений в динамических полях
     */
    updateMainAmountFromDynamicInputs: function (paymentRow) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');
        var totalAmount = 0;

        dynamicInputs.each(function () {
            var amount = window.WorkerPaymentEditCalculations.getNumericValue($(this).val());
            totalAmount += amount;
        });

        var mainAmountInput = paymentRow.find('.amount-input');
        mainAmountInput.val(totalAmount > 0 ? this.formatNumberInput(totalAmount.toString()) : '');
    },

    /**
     * Распределить введённую сумму между методами оплаты (двумя полями и т.д.)
     */
    distributeAmountToMethods: function (paymentRow, totalAmount) {
        var dynamicInputs = paymentRow.find('.dynamic-amounts input');

        if (dynamicInputs.length === 2) {
            dynamicInputs.first().val(this.formatNumberInput(totalAmount.toString()));
            dynamicInputs.last().val('');
        } else if (dynamicInputs.length > 2) {
            dynamicInputs.first().val(this.formatNumberInput(totalAmount.toString()));
            dynamicInputs.slice(1).val('');
        }
    }
};
