/**
 * Модуль UI помощников для формы редактирования платежа работнику
 * Содержит функции для работы с интерфейсом формы редактирования
 */

// Глобальный объект для UI функций редактирования
window.WorkerPaymentEditUI = {

    /**
     * Очистка всех ошибок валидации
     */
    clearValidationErrors: function () {
        $('.is-invalid').removeClass('is-invalid');
        $('.error-container').empty().hide();
        $('.alert-danger').remove();
    },

    /**
     * Очистка ошибок для конкретного поля
     */
    clearFieldErrors: function (field) {
        field.removeClass('is-invalid');
        field.closest('.form-group, td').find('.error-container').empty().hide();
    },

    /**
     * Показ ошибки для конкретного поля
     */
    showFieldError: function (field, message) {
        field.addClass('is-invalid');
        var errorContainer = field.closest('.form-group, td').find('.error-container');

        if (errorContainer.length === 0) {
            errorContainer = $('<div class="error-container"></div>');
            field.closest('.form-group, td').append(errorContainer);
        }

        errorContainer.text(message).show();
    },

    /**
     * Показ общей ошибки формы
     */
    showGeneralError: function (message) {
        var alertHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
            '<strong>Ошибка:</strong> ' + message +
            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
            '<span aria-hidden="true">&times;</span>' +
            '</button>' +
            '</div>';

        $('.worker-payment-form').prepend(alertHtml);
    },

    /**
     * Показ ошибок валидации с сервера
     */
    showServerValidationErrors: function (errors) {
        for (var field in errors) {
            if (errors.hasOwnProperty(field)) {
                var message = errors[field];

                // Ищем поле по имени или ID
                var fieldElement = $('[name="' + field + '"], #' + field);

                if (fieldElement.length > 0) {
                    this.showFieldError(fieldElement, message);
                } else {
                    // Если поле не найдено, показываем общую ошибку
                    this.showGeneralError(field + ': ' + message);
                }
            }
        }
    },

    /**
     * Создание динамических полей для ввода сумм по методам оплаты
     */
    createDynamicAmountInputs: function (row, checkedMethods) {
        var dynamicAmounts = row.find('.dynamic-amounts');
        dynamicAmounts.empty();

        checkedMethods.each(function () {
            var method = $(this);
            var methodValue = method.val();
            var methodLabel = method.closest('.form-check').find('label').text();

            var inputHtml = '<div class="form-group mb-2">' +
                '<label class="form-label">' + methodLabel + ':</label>' +
                '<input type="text" class="form-control amount-input" data-dynamic="1" ' +
                'data-method="' + methodValue + '" ' +
                'inputmode="numeric" pattern="[0-9\\s]*" ' +
                'placeholder="Сумма">' +
                '<div class="error-container"></div>' +
                '</div>';

            dynamicAmounts.append(inputHtml);
        });
    },

    /**
     * Заполнение формы существующими данными
     */
    populateFormWithExistingData: function (responseData) {
        if (responseData.payment_methods_by_type) {
            // Данные разбиты по базовому типу и методу оплаты
            for (var bType in responseData.payment_methods_by_type) {
                if (!responseData.payment_methods_by_type.hasOwnProperty(bType)) continue;

                var row = $('.payment-type-row[data-type="' + bType + '"]');
                if (!row.length) continue;

                // Активируем чекбокс типа
                var typeCheckbox = row.find('.payment-type-checkbox');
                typeCheckbox.prop('checked', true).trigger('change');

                var methodsObj = responseData.payment_methods_by_type[bType];

                // Выбираем методы платежа согласно данным
                for (var mId in methodsObj) {
                    if (!methodsObj.hasOwnProperty(mId)) continue;
                    var methodCheckbox = row.find('.payment-method-checkbox[value="' + mId + '"]');
                    if (methodCheckbox.length) {
                        methodCheckbox.prop('checked', true);
                    }
                }

                // Устанавливаем флаг загрузки данных
                row.data('loading-data', true);

                // Триггерим изменение чтобы появились динамические поля
                row.find('.payment-method-checkbox').trigger('change');

                // Заполняем значения
                for (var mId2 in methodsObj) {
                    if (!methodsObj.hasOwnProperty(mId2)) continue;
                    var amountVal = methodsObj[mId2];
                    var inputField = row.find('.dynamic-amounts input[data-method="' + mId2 + '"]');
                    if (!inputField.length) {
                        // Если один метод – это amount-input
                        inputField = row.find('.amount-input');
                    }
                    if (inputField.length && typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                        inputField.val(window.WorkerPaymentEditCalculations.formatNumber(amountVal));
                    }
                }

                // Убираем флаг загрузки данных
                setTimeout(function () {
                    row.removeData('loading-data');
                }, 100);
            }
        } else if (responseData.payments) {
            // Заполняем данные по типам платежей
            for (var typeId in responseData.payments) {
                if (responseData.payments.hasOwnProperty(typeId)) {
                    var amount = responseData.payments[typeId];

                    // Находим соответствующую строку
                    var row = $('.payment-type-row[data-type="' + typeId + '"]');
                    if (row.length > 0) {
                        // Отмечаем тип как выбранный
                        var typeCheckbox = row.find('.payment-type-checkbox');
                        typeCheckbox.prop('checked', true).trigger('change');

                        // Заполняем сумму
                        var amountInput = row.find('.amount-input');
                        if (typeof window.WorkerPaymentEditCalculations !== 'undefined') {
                            amountInput.val(window.WorkerPaymentEditCalculations.formatNumber(amount));
                        } else {
                            amountInput.val(amount);
                        }
                    }
                }
            }
        }

        // Заполняем данные о долге
        if (responseData.debt && responseData.debt > 0) {
            $('#worker-debt-amount').text(
                typeof window.WorkerPaymentEditCalculations !== 'undefined' ?
                    window.WorkerPaymentEditCalculations.formatNumber(responseData.debt) :
                    responseData.debt
            );
        }
    },

    /**
     * Обновление информационной панели
     */
    updateInfoPanel: function (workerData) {
        if (workerData.salary) {
            $('#worker-salary').text(
                typeof window.WorkerPaymentEditCalculations !== 'undefined' ?
                    window.WorkerPaymentEditCalculations.formatNumber(workerData.salary) :
                    workerData.salary
            );
        }

        if (workerData.debt) {
            $('#worker-debt-amount').text(
                typeof window.WorkerPaymentEditCalculations !== 'undefined' ?
                    window.WorkerPaymentEditCalculations.formatNumber(workerData.debt) :
                    workerData.debt
            );
        }
    },

    /**
     * Показ/скрытие секции погашения долга
     */
    toggleDebtSection: function (show) {
        var debtSection = $('#debt-payment-section');

        if (show) {
            debtSection.show();
        } else {
            debtSection.hide();
            $('#debt-payment-checkbox').prop('checked', false);
            $('#debt-payment-amount').val('');
        }
    },

    /**
     * Анимация полей с ошибками
     */
    animateError: function (field) {
        field.addClass('shake');
        setTimeout(function () {
            field.removeClass('shake');
        }, 500);
    }
};
